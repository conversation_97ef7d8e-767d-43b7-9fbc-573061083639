<template>
  <div class="v-chat-container theme-background">
    <!-- AI助手选择界面 -->
    <div v-if="currentStep === 'selection'" class="ai-selection-container">
      <div class="ai-selection-content" :style="{ background: currentAssistantBg }">
        <!-- 顶部选中的AI助手头像 -->
        <div class="selected-avatar-container">
          <img
            :src="assistants[selectedAssistantIndex].avatar"
            :alt="assistants[selectedAssistantIndex].name"
            class="selected-avatar"
          />
        </div>

        <!-- 欢迎文字 -->
        <div class="welcome-text">
          <div class="welcome-line">欢迎遇见老董</div>
          <div class="welcome-line">您的专属AI管家</div>
          <div class="welcome-line">
            {{ assistants[selectedAssistantIndex].description }}
          </div>
        </div>

        <!-- 底部Swiper选择器 -->
        <div class="avatar-swiper-container">
          <swiper
            :slides-per-view="6"
            :space-between="20"
            :centered-slides="true"
            :loop="false"
            :grab-cursor="true"
            :initial-slide="selectedAssistantIndex"
            class="avatar-swiper"
            @swiper="onSwiperInit"
            @slide-change="handleSlideChange"
          >
            <swiper-slide
              v-for="(assistant, index) in assistants"
              :key="assistant.id"
              class="avatar-slide"
              :class="{ active: index === selectedAssistantIndex }"
            >
              <img
                :src="assistant.avatar"
                :alt="assistant.name"
                class="swiper-avatar"
                @click="selectAssistant(index)"
              />
            </swiper-slide>
          </swiper>
        </div>

        <!-- 底部按钮 -->
        <div class="selection-button-container">
          <button class="selection-btn" @click="goToIntroduction">选一个形象，进一步了解</button>
        </div>
      </div>
    </div>

    <!-- AI助手介绍界面 -->
    <div v-else-if="currentStep === 'introduction'" class="ai-introduction-container">
      <div class="ai-introduction-content" :style="{ background: currentAssistantBg }">
        <div class="selected-avatar-header">
          <h2 class="welcome-title">关于老董</h2>
        </div>

        <!-- Tab切换 -->
        <div class="intro-tab-switch" @click="toggleIntroTab">
          <div class="tab-slider" :class="{ 'slide-right': activeIntroTab === 'text' }"></div>
          <div class="tab-option" :class="{ active: activeIntroTab === 'video' }">视频介绍</div>
          <div class="tab-option" :class="{ active: activeIntroTab === 'text' }">文字介绍</div>
        </div>

        <!-- 介绍内容 -->
        <div class="intro-content">
          <div v-if="activeIntroTab === 'video'" class="video-intro">
            <div class="video-container">
              <video
                class="intro-video"
                controls
                preload="metadata"
                :poster="assistants[selectedAssistantIndex].avatar"
              >
                <source
                  src="https://s3plus.meituan.net/mcopilot-pub/70f2874b2cd675e9d6d2b76eb80cd275.mp4"
                  type="video/mp4"
                />
                您的浏览器不支持视频播放。
              </video>
            </div>
          </div>
          <div v-else class="text-intro">
            <p>{{ assistants[selectedAssistantIndex].detailedDescription }}</p>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="introduction-button-container">
          <button class="introduction-btn" @click="startChat">培养老董，开始聊天/安排任务</button>
        </div>
        <!-- 底部欢迎语 -->
        <div class="introduction-bottom-text">
          <p>让我们开始这段美好的旅程~</p>
        </div>
      </div>
    </div>

    <!-- 聊天界面 -->
    <div v-else-if="currentStep === 'chat'" class="welcome-container chat-mode">
      <div class="welcome-content chat-content" :style="{ background: currentAssistantBg }">
        <div class="welcome-header chat-header">
          <div class="welcome-title-row">
            <div class="welcome-icon">
              <img
                :src="assistants[selectedAssistantIndex].avatar"
                :alt="assistants[selectedAssistantIndex].name"
                class="assistant-avatar"
              />
            </div>
            <div class="cyber-title">老董</div>
            <div class="header-right">
              <div
                class="intimacy-display"
                title="点击查看懂量说明"
                style="cursor: pointer"
                @click="showIntimacyHelp = true"
              >
                <span class="intimacy-label">当前懂量：</span>
                <span class="intimacy-value">{{ intimacyData.intimacy_score }}</span>
              </div>
              <!-- 调色板：点击展开主题选择面板 -->
              <div ref="themeSelectorRef" class="theme-selector-wrapper">
                <button
                  class="theme-switch-btn"
                  :title="`选择主题（当前：${currentTheme.name}）`"
                  @click.stop="toggleThemePanel"
                >
                  <!-- 使用新的主题切换SVG图标 -->
                  <ThemeSwitchIcon :size="18" :color="currentTheme.colors.primaryColor" />
                  <span class="theme-name">调色板</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- AI助手功能排列式布局 -->
        <div class="ai-assistant-grid-layout">
          <!-- 功能区域 -->
          <div class="features-grid">
            <!-- 第一行：5个头像 -->
            <div class="features-row row1">
              <div
                v-for="item in row1Features"
                :key="item.id"
                class="feature-item"
                @click="item.handler"
              >
                <div class="avatar-circle" :class="{ disabled: !item.completed }">
                  <img :src="item.image" :alt="item.title" class="feature-avatar-img" />
                </div>
                <div class="feature-label" :class="{ 'incomplete-label': !item.completed }">
                  {{ item.title }}
                </div>
              </div>
            </div>

            <!-- 第二行：4个头像 -->
            <div class="features-row row2">
              <div
                v-for="item in row2Features"
                :key="item.id"
                class="feature-item"
                @click="item.handler"
              >
                <div class="avatar-circle" :class="{ disabled: !item.completed }">
                  <img :src="item.image" :alt="item.title" class="feature-avatar-img" />
                </div>
                <div class="feature-label" :class="{ 'incomplete-label': !item.completed }">
                  {{ item.title }}
                </div>
              </div>
            </div>

            <!-- 第三行：3个头像 -->
            <div class="features-row row3">
              <div
                v-for="item in row3Features"
                :key="item.id"
                class="feature-item"
                @click="item.handler"
              >
                <div class="avatar-circle" :class="{ disabled: !item.completed }">
                  <img :src="item.image" :alt="item.title" class="feature-avatar-img" />
                </div>
                <div class="feature-label" :class="{ 'incomplete-label': !item.completed }">
                  {{ item.title }}
                </div>
              </div>
            </div>

            <!-- 中心管家头像 -->
            <div class="main-assistant-avatar" @click="handleRecordMasterSituation">
              <div class="avatar-circle main-avatar">
                <img
                  :src="assistants[selectedAssistantIndex].avatar"
                  :alt="assistants[selectedAssistantIndex].name"
                  class="main-avatar-img"
                />
              </div>
              <div class="main-avatar-label">老董</div>

              <!-- 使用 AssistantIntroduction 组件替换嵌入的助手介绍部分 -->
              <AssistantIntroduction
                :assistant-intros="assistantIntros"
                :available-features="row3Features"
                :future-features="row2Features"
                :top-features="row1Features"
              />
            </div>
          </div>
        </div>

        <!-- 亲密度显示模块（默认隐藏，改用弹窗展示） -->
        <div v-if="false" class="intimacy-container chat-intimacy">
          <div class="intimacy-content">
            <!-- 左侧曲线图标 -->
            <div class="intimacy-curve">
              <img :src="polylineIcon" alt="懂量曲线" class="polyline-icon" />
            </div>
            <div class="intimacy-text-container">
              <div class="intimacy-title">关于“懂量”</div>
              <div class="intimacy-description">
                “懂量”代表了我对您的了解程度。与我交流越多，懂量提升越快，我能为您解锁的功能也越多！
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主题选择面板（移到welcome-container内部，避免被助手层挡住） -->
      <ThemePanel
        v-if="showThemePanel"
        ref="themePanelRef"
        :visible="showThemePanel"
        @theme-select="handleThemeSelect"
        @close="showThemePanel = false"
      />
    </div>

    <!-- 底部输入框 - 仅在chat模式下显示，固定在v-chat-container内部底部 -->
    <div v-if="currentStep === 'chat'" class="footer">
      <!-- 输入框 -->
      <form class="input-wrapper" action="" @submit.prevent="handleFormSubmit">
        <inputBar
          ref="inputBarRef"
          @voice-send="handleInputSend"
          @get-started="handleGetStarted"
          @send="handleInputSend"
          @stop="handleStop"
          @recording-status="handleRecordingStatus"
        />
      </form>

      <!-- 老董假装说话样式 - 直接放在输入框下方 -->
      <div class="laodong-fake-speaking">
        <div class="fake-speaking-container">
          <div class="laodong-avatar">
            <img :src="selectedAssistantAvatar" alt="老董头像" />
          </div>
          <div class="fake-speaking-content">
            <div class="fake-speaking-text">老董会根据您的问题给出专业建议</div>
            <div class="fake-speaking-dots">
              <span class="dot"></span>
              <span class="dot"></span>
              <span class="dot"></span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 对话展示组件 - 仅在chat模式下显示 -->
    <ChatDialog
      v-if="currentStep === 'chat'"
      :visible="showChatDialog"
      :messages="chatMessages"
      :conversation-id="conversationId"
      :user-id="currentUserId"
      @close="handleCloseChatDialog"
      @send-message="handleChatDialogSend"
      @regenerate="handleRegenerate"
      @new-chat="clearChatSession"
    />

    <!-- 亲密度说明弹窗 -->
    <div v-if="showIntimacyHelp" class="intimacy-popup-overlay" @click="showIntimacyHelp = false">
      <div class="intimacy-popup-content" :style="{ background: currentAssistantBg }" @click.stop>
        <!-- 关闭按钮 - 移到最右上角 -->
        <button class="intimacy-close-btn" @click="showIntimacyHelp = false">
          <DeleteIcon :size="24" color="var(--primary-color)" />
        </button>
        <!-- 顶部选中的AI助手头像 -->
        <div class="intimacy-popup-avatar-container">
          <img
            :src="assistants[selectedAssistantIndex].avatar"
            :alt="assistants[selectedAssistantIndex].name"
            class="intimacy-popup-avatar"
          />
        </div>
        <div class="intimacy-popup-header">
          <h3 class="intimacy-popup-title">懂量说明</h3>
        </div>
        <div class="intimacy-popup-body">
          <p class="intimacy-description">
            “懂量”代表了我对您的了解程度。与我交流越多，懂量提升越快，我能为您解锁的功能也越多！
          </p>
        </div>
      </div>
    </div>

    <!-- 功能锁定提示弹窗 -->
    <div v-if="showLockDialog" class="dialog-overlay">
      <div class="dialog-container">
        <div class="dialog-header">
          <div class="dialog-title">功能锁定</div>
        </div>
        <div class="dialog-content">
          <div class="lock-message">即将上线，敬请期待</div>
        </div>
        <div class="dialog-footer">
          <button class="confirm-btn" @click="closeLockDialog">我知道了</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { Swiper, SwiperSlide } from 'swiper/vue';
import type { Swiper as SwiperType } from 'swiper';
import 'swiper/css';

import inputBar from '@/components/Chat/inputBar.vue';
import ChatDialog from '@/components/Dialogs/ChatDialog.vue';
import ThemePanel from '@/components/Chat/ThemePanel.vue';
import AssistantIntroduction from '@/components/Chat/AssistantIntroduction.vue';
import { getUserInfo } from '@/apis/common';
import { getIntimacy } from '@/apis/memory';
import { streamChat, createConversation, type IToolCall } from '@/apis/chat';
import { Typewriter } from '@/utils/typeWriter';
import { useChatStore } from '@/stores/chat';
import { useThemeStore } from '@/stores/theme';
import { AnswerStatusEnum } from '@/constants/chat';
import { initSceneEffects } from '@/utils/sceneEffects';
// 导入头像图片
import avatar1 from '@/assets/icon/laodong1.jpg';
import avatar2 from '@/assets/icon/laodong2.jpg';
import avatar3 from '@/assets/icon/laodong3.png';
import avatar4 from '@/assets/icon/laodong4.png';
import avatar5 from '@/assets/icon/laodong5.png';
import avatar6 from '@/assets/icon/laodong6.jpg';
import polylineIcon from '@/assets/icon/polyline.png';
// 导入助手功能图片
import relationImg from '@/assets/assistant/relation.png';
import questionImg from '@/assets/assistant/question.png';
import weatherImg from '@/assets/assistant/weather.jpg';
import dressImg from '@/assets/assistant/dress.jpg';
import financeImg from '@/assets/assistant/finance.jpg';
import takeOutImg from '@/assets/assistant/take-out.jpg';
import videoImg from '@/assets/assistant/video.jpg';
import DeleteIcon from '@/assets/icons/DeleteIcon.vue';
import ThemeSwitchIcon from '@/assets/icons/ThemeSwitchIcon.vue';
// 主题预设：氛围样式与色系


const router = useRouter();

// 用户信息
const currentUserId = ref('');

// 亲密度弹窗控制
const showIntimacyHelp = ref(false);

// 功能锁定弹窗控制
const showLockDialog = ref(false);

// 聊天相关状态
const showChatDialog = ref(false);
const chatMessages = ref<IChatStreamContent[]>([]);

// 第一行：5个头像
const row1Features = ref([
  { id: 101, title: '董学习', image: videoImg, completed: false, handler: handleShowLockDialog },
  { id: 102, title: '董出行', image: weatherImg, completed: false, handler: handleShowLockDialog },
  { id: 103, title: '董健身', image: dressImg, completed: false, handler: handleShowLockDialog },
  { id: 104, title: '董美食', image: takeOutImg, completed: false, handler: handleShowLockDialog },
  { id: 105, title: '董家务', image: relationImg, completed: false, handler: handleShowLockDialog },
]);

// 第二行：4个头像
const row2Features = ref([
  { id: 4, title: '董理财', image: financeImg, completed: false, handler: handleShowLockDialog },
  { id: 10, title: '董追剧', image: videoImg, completed: true, handler: handleDongZhuiju },
  { id: 9, title: '董外卖', image: takeOutImg, completed: false, handler: handleShowLockDialog },
  { id: 8, title: '董穿搭', image: dressImg, completed: false, handler: handleShowLockDialog },
]);

// 第三行：3个头像
const row3Features = ref([
  { id: 1, title: '董亲友', image: relationImg, completed: true, handler: handleMyRelations },
  { id: 2, title: '董问题', image: questionImg, completed: true, handler: handleChatWithMe },
  { id: 3, title: '董天奇', image: weatherImg, completed: true, handler: handleWeatherForecast },
]);

const conversationId = ref('');
const isRecording = ref(false);
const inputBarRef = ref();

// 聊天相关状态 - 与relationGraph.vue保持一致
const chatStore = useChatStore();

// 主题相关状态
const themeStore = useThemeStore();
const currentTheme = computed(() => themeStore.currentTheme);
// 调色板面板控制与数据
const showThemePanel = ref(false);
const themeSelectorRef = ref<HTMLElement | null>(null);



const toggleThemePanel = () => {
  showThemePanel.value = !showThemePanel.value;
};

const handleThemeSelect = (themeId: string) => {
  void themeStore.switchTheme(themeId);
  showThemePanel.value = false;
  console.log(`🎨 [index.vue] 选择主题: ${themeId}`);
};
// 点击空白处关闭主题面板（忽略面板内部点击）
const handleGlobalClick = (e: MouseEvent) => {
  if (!showThemePanel.value) return;
  const root = themeSelectorRef.value;
  const target = e.target as Node | null;
  if (root && target && root.contains(target)) return;
  showThemePanel.value = false;
};
const isStoppedByUser = ref(false);
const streamController = ref<AbortController | null>(null);
const isTypewriterStarted = ref(false);

// 创建打字机实例 - 与relationGraph.vue保持一致
const typewriter = new Typewriter(
  (str: string) => {
    if (str && chatMessages.value.length > 0) {
      const lastMessage = chatMessages.value[chatMessages.value.length - 1];
      if (lastMessage && lastMessage.role === 'assistant') {
        lastMessage.content = str;
      }
    }
  },
  () => {
    // 聊天完成回调
    console.log('✅ [index.vue] 聊天完成');
    chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
  },
);

// 懂量数据
const intimacyData = ref({
  intimacy_score: 0,
  level: '',
  next_level: '',
  progress_to_next: 0,
  points_needed: 0,
});

// 懂量加载状态
const intimacyLoading = ref(false);

// AI助理头像选择的存储键
const AI_ASSISTANT_STORAGE_KEY = 'selectedAssistantIndex';
const AI_STEP_STORAGE_KEY = 'currentStep';

// 初始化函数：同步确定初始状态，避免闪烁
const getInitialStep = (): 'selection' | 'introduction' | 'chat' => {
  // 检查是否从home按钮跳转过来
  const fromHome = sessionStorage.getItem('fromHomeButton');
  if (fromHome === 'true') {
    return 'chat';
  }

  // 检查用户是否已经选择过AI助手
  const savedAssistant = localStorage.getItem(AI_ASSISTANT_STORAGE_KEY);
  const savedStep = localStorage.getItem(AI_STEP_STORAGE_KEY);

  if (savedAssistant !== null) {
    // 如果用户已经选择过AI助手，检查保存的步骤状态
    if (savedStep === 'chat') {
      // 如果用户已经到达过聊天界面，直接进入聊天界面
      console.log('✅ [index.vue] 用户已完成选择流程，直接进入聊天界面');
      return 'chat';
    }
    if (savedStep === 'introduction') {
      // 如果用户在介绍界面，恢复到介绍界面
      console.log('🔄 [index.vue] 恢复到AI助手介绍界面');
      return 'introduction';
    }
    // 如果没有步骤记录但有助手选择，可能是第一次选择，进入介绍界面
    console.log('🔄 [index.vue] 用户已选择助手但未完成流程，进入介绍界面');
    return 'introduction';
  }

  // 如果用户从未选择过AI助手，进入选择界面
  console.log('⚠️ [index.vue] 用户未选择过AI助手，进入选择界面');
  return 'selection';
};

const getInitialAssistantIndex = (): number => {
  // 总是尝试从localStorage读取助手选择
  const saved = localStorage.getItem(AI_ASSISTANT_STORAGE_KEY);
  console.log('🔍 [index.vue] getInitialAssistantIndex - localStorage值:', saved);
  if (saved !== null) {
    const index = parseInt(saved, 10);
    console.log('🔍 [index.vue] getInitialAssistantIndex - 解析的索引:', index);
    // 这里先用一个合理的范围检查，后面会在组件挂载时进一步验证
    if (index >= 0 && index < 7) {
      console.log('✅ [index.vue] 从localStorage恢复助手选择:', index);
      return index;
    }
  }

  // 如果没有保存的选择，默认为第一个助理
  console.log('⚠️ [index.vue] 使用默认助手选择: 0');
  return 3;
};

// 当前步骤：selection(选择) -> introduction(介绍) -> chat(聊天)
const currentStep = ref<'selection' | 'introduction' | 'chat'>(getInitialStep());

// 当前选中的AI助手索引
const selectedAssistantIndex = ref(getInitialAssistantIndex());

// 介绍页面的Tab状态
const activeIntroTab = ref<'video' | 'text'>('video');

// Swiper实例引用
const swiperInstance = ref<SwiperType>();

// AI助手通用详细描述
const ASSISTANT_DETAILED_DESCRIPTION = `这一波AI火热2年多了，
可是还没有一个靠谱的个人助手。
为什么呢？
因为大多数AI助手没有关注用户个人的情况。

这些AI助手，
就类似于博物馆的讲解员。
他们，上知天文、下知地理，博学多才。
可惜，不知道你喜欢吃什么、
不知道去过哪里、
不知道你对什么感兴趣、
也不知道你工作中的烦恼……

我是老董，你的AI管家。
我有如下特点：
1、首先是了解你的情况，用小本本把你的情况、你亲友的情况都记下来，懂你才能服务好你；
2、我可以召唤其他AI助手，例如查天气的、订外卖的、订酒店的….. 一起为你服务。
我不是简单召唤他们，不是发送"明升今晚想点外卖火锅"这样简单的需求。
而是发送很多背景资料给他们，例如
"明升，xx岁，喜欢吃xxxx、不喜欢吃xxxx，
今晚和他一起吃饭的是他12岁的女儿和4岁的儿子。
女儿喜欢吃牛肉片、午餐肉、不喜欢吃麻酱、不喜欢吃内脏，；
儿子喜欢吃牛肉片、午餐肉还有烧饼、不需要蘸料，
过去一年，周日晚上在家点外卖的订单有30次，其中6次是火锅，这6次订单的情况是xxxx；
这顿饭在100元～200元之间都可以，希望19点左右送到；"
3、我接受了职业培训，知道有些事情不能问、有些事情不能说，有些事情不能记，以及不泄漏用户隐私。
4、我记录的信息，后续你可以修改、可以删除也可以下载备份。
5、类似养成游戏，你刚刚见到我的时候，我是小董，我们聊多了我就可以成长，变成大董、老董…..

现在，我们开始聊聊吧～

【重要提示】
用户输入的内容，尤其是语音模式的，可能存在错别字、同音字、近似词语以及口头禅、脏话，以及标点符号错误。老董会智能识别并纠正这些问题，确保理解用户的真实意图。`;

// AI助手数据
const assistants = ref([
  {
    id: 1,
    name: '老董',
    avatar: avatar1,
    description: '懂你+懂干活=更好的助手',
    detailedDescription: ASSISTANT_DETAILED_DESCRIPTION,
    bgColor: 'rgba(0, 0, 0, 0.2)', // 黑色，降低透明度
  },
  {
    id: 2,
    name: '老董',
    avatar: avatar2,
    description: '懂你+懂干活=更好的助手',
    detailedDescription: ASSISTANT_DETAILED_DESCRIPTION,
    bgColor: 'rgba(147, 197, 253, 0.15)', // 浅蓝色，降低透明度
  },
  {
    id: 3,
    name: '老董',
    avatar: avatar3,
    description: '懂你+懂干活=更好的助手',
    detailedDescription: ASSISTANT_DETAILED_DESCRIPTION,
    bgColor: 'rgba(30, 58, 138, 0.2)', // 深蓝色，降低透明度
  },
  {
    id: 4,
    name: '老董',
    avatar: avatar4,
    description: '懂你+懂干活=更好的助手',
    detailedDescription: ASSISTANT_DETAILED_DESCRIPTION,
    bgColor: 'rgba(191, 219, 254, 0.1)', // 极浅蓝色，降低透明度
  },
  {
    id: 5,
    name: '老董',
    avatar: avatar5,
    description: '懂你+懂干活=更好的助手',
    detailedDescription: ASSISTANT_DETAILED_DESCRIPTION,
    bgColor: 'rgba(0, 0, 0, 0.2)', // 黑色，降低透明度 (重复laodong1)
  },
  {
    id: 6,
    name: '老董',
    avatar: avatar6,
    description: '懂你+懂干活=更好的助手',
    detailedDescription: ASSISTANT_DETAILED_DESCRIPTION,
    bgColor: 'rgba(147, 197, 253, 0.15)', // 浅蓝色，降低透明度 (重复laodong2)
  },
]);

// 当前助手背景色
const currentAssistantBg = computed(() => {
  return assistants.value[selectedAssistantIndex.value].bgColor;
});

// 获取选中的助手头像
const selectedAssistantAvatar = computed(() => {
  const index = selectedAssistantIndex.value;
  if (index >= 0 && index < assistants.value.length) {
    console.log(
      '✅ [index.vue] 计算属性返回助手头像:',
      assistants.value[index].name,
      assistants.value[index].avatar,
    );
    return assistants.value[index].avatar;
  }
  console.log(
    '⚠️ [index.vue] 计算属性返回默认助手头像:',
    assistants.value[0].name,
    assistants.value[0].avatar,
  );
  return assistants.value[0].avatar;
});

// AI助理头像选择的持久化存储函数
const saveAssistantSelection = (index: number) => {
  localStorage.setItem(AI_ASSISTANT_STORAGE_KEY, index.toString());
  console.log('💾 [index.vue] 保存AI助理选择:', index);
  // 立即验证保存是否成功
  const saved = localStorage.getItem(AI_ASSISTANT_STORAGE_KEY);
  console.log('🔍 [index.vue] 保存后立即读取localStorage:', saved);
};

const saveCurrentStep = (step: 'selection' | 'introduction' | 'chat') => {
  localStorage.setItem(AI_STEP_STORAGE_KEY, step);
  console.log('💾 [index.vue] 保存当前步骤:', step);
};

// AI助手选择相关方法
const selectAssistant = (index: number) => {
  console.log('🎯 [index.vue] selectAssistant 被调用，索引:', index);
  selectedAssistantIndex.value = index;
  // 立即保存选择到localStorage
  saveAssistantSelection(index);
  console.log(
    '💾 [index.vue] selectAssistant 保存后，localStorage值:',
    localStorage.getItem(AI_ASSISTANT_STORAGE_KEY),
  );
  // 让选中的头像滑动到中央
  if (swiperInstance.value) {
    swiperInstance.value.slideTo(index);
  }
};

const handleSlideChange = (swiper: { activeIndex: number }) => {
  console.log('🔄 [index.vue] handleSlideChange 被调用，索引:', swiper.activeIndex);
  selectedAssistantIndex.value = swiper.activeIndex;
  // 立即保存选择到localStorage
  saveAssistantSelection(swiper.activeIndex);
  console.log(
    '💾 [index.vue] handleSlideChange 保存后，localStorage值:',
    localStorage.getItem(AI_ASSISTANT_STORAGE_KEY),
  );
};

// Swiper初始化回调
const onSwiperInit = (swiper: SwiperType) => {
  swiperInstance.value = swiper;
};

const goToIntroduction = () => {
  currentStep.value = 'introduction';
  saveCurrentStep('introduction');
};

// 介绍页面Tab切换
const toggleIntroTab = () => {
  activeIntroTab.value = activeIntroTab.value === 'video' ? 'text' : 'video';
};

const startChat = () => {
  currentStep.value = 'chat';
  saveCurrentStep('chat');
};

// 直接跳转到聊天界面的方法（供home按钮使用）
const goToChat = () => {
  currentStep.value = 'chat';
  saveCurrentStep('chat');
  console.log('🏠 [index.vue] 直接跳转到聊天界面');
};

// 圆形布局相关函数已移除（改为排列式布局）

// 处理我的亲友点击
async function handleMyRelations() {
  console.log('🔄 [index.vue] 我的亲友卡片点击，跳转到关系图谱页面');

  // 清理之前的功能标记
  sessionStorage.removeItem('chatFeature');
  // 记录来源页面为index
  sessionStorage.setItem('relationGraphSource', 'index');
  // 标记功能来源用于头像切换
  sessionStorage.setItem('chatFeature', 'relation');

  await router.push({
    name: 'relationship-graph',
  });
}

// 处理天气预报点击
async function handleWeatherForecast() {
  console.log('🔄 [index.vue] 天气预报卡片点击，跳转到聊天页面');
  // 清理之前的功能标记
  sessionStorage.removeItem('chatFeature');
  // 标记功能来源用于聊天页头像切换
  sessionStorage.setItem('chatFeature', 'weather');

  await router.push({
    name: 'chat-conversation',
    params: { title: 'new' },
    query: { cardType: 'weather' },
  });
}

// 处理董追剧点击
function handleDongZhuiju() {
  console.log('🔄 [index.vue] 董追剧卡片点击，跳转到外部页面');
  window.open('http://**************:8000/', '_blank');
}



// 处理和我聊天点击
async function handleChatWithMe() {
  console.log('🔄 [index.vue] 和我聊天卡片点击，直接跳转到聊天页面');
  // 清理之前的功能标记
  sessionStorage.removeItem('chatFeature');
  // 标记功能来源用于聊天页头像切换
  sessionStorage.setItem('chatFeature', 'question');

  await router.push({
    name: 'chat-conversation',
    params: { title: 'new' },
    query: { cardType: 'question' },
  });
}

// 处理记录您的情况点击
async function handleRecordMasterSituation() {
  console.log('🔄 [index.vue] 记录您的情况卡片点击，跳转到聊天页面');
  // 清理之前的功能标记
  sessionStorage.removeItem('chatFeature');
  // 标记功能来源用于聊天页头像切换
  sessionStorage.setItem('chatFeature', 'laodong');

  await router.push({
    name: 'chat-conversation',
    params: { title: 'new' },
    query: { cardType: 'record' },
  });
}

// 处理显示功能锁定弹窗
function handleShowLockDialog() {
  console.log('🔄 [index.vue] 显示功能锁定弹窗');
  showLockDialog.value = true;
}

// 关闭功能锁定弹窗
function closeLockDialog() {
  console.log('🔄 [index.vue] 关闭功能锁定弹窗');
  showLockDialog.value = false;
}

// 获取用户信息
const loadUserInfo = async () => {
  try {
    console.log('🔄 [index.vue] 开始获取用户信息...');
    const userInfo = await getUserInfo();
    console.log('📡 [index.vue] 用户信息:', userInfo);

    if (userInfo && userInfo.login) {
      currentUserId.value = userInfo.login;
      console.log('✅ [index.vue] 用户信息加载成功, userId:', currentUserId.value);
    } else {
      console.warn('⚠️ [index.vue] 用户信息格式异常');
      currentUserId.value = 'unknown_user';
    }
  } catch (error) {
    console.error('❌ [index.vue] 获取用户信息失败:', error);
    currentUserId.value = 'unknown_user';
  }
};

// 获取懂量数据
const loadIntimacyData = async () => {
  if (!currentUserId.value || currentUserId.value === 'unknown_user') {
    console.warn('⚠️ [index.vue] 用户ID无效，跳过懂量数据获取');
    return;
  }

  try {
    console.log('🔄 [index.vue] 开始获取懂量数据...');
    intimacyLoading.value = true;

    const response = await getIntimacy({
      user_id: currentUserId.value,
    });

    console.log('📡 [index.vue] 懂量数据响应:', response);

    if (response && response.result === 'success') {
      intimacyData.value = {
        intimacy_score: response.intimacy_score,
        level: response.level,
        next_level: response.next_level,
        progress_to_next: response.progress_to_next,
        points_needed: response.points_needed,
      };
      console.log('✅ [index.vue] 懂量数据加载成功:', intimacyData.value);
    } else {
      console.warn('⚠️ [index.vue] 懂量数据响应格式异常:', response);
    }
  } catch (error) {
    console.error('❌ [index.vue] 获取懂量数据失败:', error);
  } finally {
    intimacyLoading.value = false;
  }
};

onMounted(async () => {
  console.log('🔄 [index.vue] onMounted 开始');
  console.log('🔍 [index.vue] onMounted - 当前助手索引:', selectedAssistantIndex.value);
  console.log('🔍 [index.vue] onMounted - 助手数组长度:', assistants.value.length);
  console.log('🔍 [index.vue] onMounted - localStorage值:', localStorage.getItem(AI_ASSISTANT_STORAGE_KEY));

  // 加载用户信息
  await loadUserInfo();

  // 初始化主题（设置用户ID以支持后端同步）
  if (currentUserId.value && currentUserId.value !== 'unknown_user') {
    themeStore.setUserId(currentUserId.value);
  }
  await themeStore.initTheme();

  // 加载懂量数据
  await loadIntimacyData();

  // 验证助手索引是否在有效范围内
  if (selectedAssistantIndex.value >= assistants.value.length) {
    console.log(
      '⚠️ [index.vue] 助手索引超出范围，当前索引:',
      selectedAssistantIndex.value,
      '数组长度:',
      assistants.value.length,
    );
    selectedAssistantIndex.value = 0;
    saveAssistantSelection(0);
    console.log('🔧 [index.vue] 助手索引超出范围，重置为0');
  } else {
    console.log('✅ [index.vue] 助手索引在有效范围内:', selectedAssistantIndex.value);
  }

  // 检查是否从home按钮跳转过来
  const fromHome = sessionStorage.getItem('fromHomeButton');
  if (fromHome === 'true') {
    sessionStorage.removeItem('fromHomeButton');
    console.log('🏠 [index.vue] 从home按钮跳转，保持之前的AI助理选择');
  } else {
    // 页面刷新时保留AI助理选择和步骤状态，实现持久化
    console.log('🔄 [index.vue] 页面刷新，保留AI助理选择和步骤状态');
  }

  // 确保助手选择已保存（如果用户之前选择过）
  if (selectedAssistantIndex.value !== 0) {
    saveAssistantSelection(selectedAssistantIndex.value);
    console.log('� [index.vue] 确保助手选择已保存:', selectedAssistantIndex.value);
  }

  console.log('✅ [index.vue] onMounted 完成，最终助手索引:', selectedAssistantIndex.value);

  // 初始化场景特效（仅在 chat 界面）
  if (currentStep.value === 'chat') {
    const root = document.querySelector('.welcome-content.chat-content');
    if (root instanceof HTMLElement) {
      disposeSceneEffects = initSceneEffects(root);
    }
  }
  // 注册全局点击以关闭主题面板
  document.addEventListener('click', handleGlobalClick);
});

// 卸载时移除全局监听
onUnmounted(() => {
  document.removeEventListener('click', handleGlobalClick);
});

// 监听步骤变化，进入/离开 chat 时启停场景特效
watch(currentStep, (step) => {
  if (step === 'chat') {
    // 重新初始化，避免重复
    if (disposeSceneEffects) {
      disposeSceneEffects();
      disposeSceneEffects = null;
    }
    const root = document.querySelector('.welcome-content.chat-content');
    if (root instanceof HTMLElement) {
      disposeSceneEffects = initSceneEffects(root);
    }
  } else if (disposeSceneEffects) {
    disposeSceneEffects();
    disposeSceneEffects = null;
  }
});

// ——— 助手介绍文案 ———
const assistantIntros = [
  '董亲友：帮我记录亲友信息、重要日期与喜好，安排走动更贴心；自动生成纪念日提醒与礼物清单，见面前快速复习关系要点，让每次联系都有温度。',
  '董问题：随时问，随时答，还能把你的上下文记在小本本里；跨对话持续记忆，归纳要点与待办清单，下一次接着聊不重头来。',
  '董天奇：洞察天气与出行建议，提醒穿搭与备伞；结合通勤与行程智能推送，极端天气提前预警并给出备用路线与时间建议。',
  '董理财：根据你的偏好与目标，给出理财建议与提醒；跟踪现金流与风险敞口，定期复盘收益，提供再平衡与自动储蓄方案。',
  '董追剧：整理片单、续看进度，给你不踩雷的推荐；融合口碑与你的观影历史，自动生成周末清单与新剧开播提醒。',
  '董外卖：结合你和家人的口味与历史订单，智能下单更省心；避开忌口与高热量时段，搭配营养均衡和优惠组合，省钱又健康。',
  '董穿搭：结合天气与场景，安排每日穿搭建议；提供配色与单品替代，旅行出差一键生成打包清单，省时不踩坑。',
  '董学习：根据你的目标拆解学习路径，智能生成每日学习清单；结合遗忘曲线安排复习提醒，输出笔记与测验巩固成效。',
  '董出行：结合实时交通与天气，给出最佳路线与时间建议；自动同步行程，提前预警延误与拥堵，并提供备选方案。',
  '董健身：根据体况与目标生成训练计划；记录训练数据与恢复状态，智能安排强度与饮食建议，避免伤害更高效。',
  '董美食：结合口味与健康目标推荐菜谱；自动生成购物清单与做菜步骤，节日/聚会给出应景菜单与备菜节奏。',
  '董家务：按居住习惯生成家务清单与周期提醒；租房/搬家/保洁等任务一键安排，避免遗漏，更省心。',
];
// 场景特效释放函数（进入chat初始化，离开或卸载时释放）
let disposeSceneEffects: (() => void) | null = null;

onUnmounted(() => {
  if (disposeSceneEffects) {
    disposeSceneEffects();
    disposeSceneEffects = null;
  }
});

// 发送聊天消息的方法 - 与relationGraph.vue完全一致
const sendChatMessage = async (messageContent: string) => {
  if (!messageContent.trim() || !currentUserId.value) {
    return;
  }

  console.log('� [index.vue] 开始发送聊天消息:', messageContent);

  // 如果有正在进行的请求，先取消它
  if (streamController.value) {
    console.log('🔄 [index.vue] 取消正在进行的请求');
    streamController.value.abort();
    streamController.value = null;
  }

  // 重置状态
  isStoppedByUser.value = false;
  isTypewriterStarted.value = false;

  // 设置加载状态
  chatStore.setAnswerStatus(AnswerStatusEnum.LOADING);

  // 添加助手消息占位符
  const assistantMessage: IChatStreamContent = {
    role: 'assistant',
    content: '',
    key: Date.now() + 1,
    isFinish: false,
    reasoningData: {} as IReasoningData,
    isToolCallLoading: false,
  };
  chatMessages.value.push(assistantMessage);

  try {
    // 构建请求数据
    const requestData: IChatRequest = {
      content: messageContent,
      conversation_id: conversationId.value,
      user_id: currentUserId.value,
    };

    // 创建新的 AbortController
    streamController.value = new AbortController();

    // 开始流式聊天
    await streamChat(
      requestData,
      {
        onMessage: (content: string, isFinal: boolean) => {
          if (isStoppedByUser.value) {
            console.log('🚫 [index.vue] 用户已停止，忽略消息片段');
            return;
          }

          // 添加消息到打字机队列
          typewriter.add(content);

          // 启动打字机（如果还未启动）
          if (!isTypewriterStarted.value) {
            console.log('🚀 [index.vue] 启动typewriter，开始显示内容');
            isTypewriterStarted.value = true;
            typewriter.start();
          }

          if (isFinal) {
            console.log('✅ [index.vue] 收到最终消息，标记完成');
            typewriter.markFinished();
            const lastMessage = chatMessages.value[chatMessages.value.length - 1];
            if (lastMessage && lastMessage.role === 'assistant') {
              lastMessage.isFinish = true;
            }
          }
        },
        onPreResponse: (responseContent: string, stage: string) => {
          console.log('📝 [index.vue] 预响应:', {
            content: responseContent,
            stage,
          });
        },
        onToolCall: (toolCall: IToolCall) => {
          console.log('🔧 [index.vue] 工具调用:', toolCall);
        },
        onRecommendations: (recommendations: string[]) => {
          console.log('💡 [index.vue] 推荐问题:', recommendations);
        },
        onEnd: () => {
          console.log('✅ [index.vue] 聊天流结束');
          chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
        },
        onError: (error: Error) => {
          console.error('❌ [index.vue] 聊天错误:', error);
          handleChatError('聊天过程中发生错误，请稍后重试');
        },
        onClose: () => {
          console.log('🔒 [index.vue] 聊天连接关闭');
          // 连接关闭时重置 streamController，但保持会话数据
          streamController.value = null;
        },
      },
      streamController.value.signal,
    );
  } catch (error) {
    console.error('❌ [index.vue] 发送聊天消息失败:', error);
    handleChatError('发送消息失败，请稍后重试');
  }
};

// 处理聊天错误 - 与relationGraph.vue完全一致
const handleChatError = (errorText: string) => {
  console.log('❌ [index.vue] 处理聊天错误:', errorText);

  const lastMessage = chatMessages.value[chatMessages.value.length - 1];
  if (lastMessage && lastMessage.role === 'assistant' && !lastMessage.isFinish) {
    lastMessage.content = errorText;
    lastMessage.isFinish = true;
    lastMessage.key = Date.now();
  } else {
    chatMessages.value.push({
      role: 'assistant',
      content: errorText,
      key: Date.now(),
      isFinish: true,
      reasoningData: {} as IReasoningData,
    });
  }

  // 结束打字机
  typewriter.markFinished();
  setTimeout(() => {
    typewriter.done();
  }, 100);

  // 重置状态，但保持会话数据
  setTimeout(() => {
    chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
    isTypewriterStarted.value = false;
    // 不清除 streamController，让它在下次发送时重新创建
  }, 1000);
};

// 处理输入发送 - 与relationGraph.vue完全一致
const handleInputSend = async (message: string) => {
  console.log('🔄 [index.vue] 收到输入消息:', message);

  if (!message.trim()) {
    return;
  }

  // 如果没有会话ID，先创建会话
  if (!conversationId.value) {
    try {
      console.log('🔄 [index.vue] 创建新会话...');
      const response = await createConversation({
        user_id: currentUserId.value,
      });

      if (response.success && response.conversation_id) {
        conversationId.value = response.conversation_id;
        console.log('✅ [index.vue] 会话创建成功，会话ID:', conversationId.value);
      } else {
        console.error('❌ [index.vue] 会话创建失败，使用临时ID');
        conversationId.value = `conv_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      }
    } catch (error) {
      console.error('❌ [index.vue] 创建会话失败:', error);
      conversationId.value = `conv_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }
  } else {
    console.log('🔄 [index.vue] 使用现有会话ID:', conversationId.value);
  }

  // 显示对话框
  showChatDialog.value = true;

  // 添加用户消息到聊天列表
  chatMessages.value.push({
    role: 'user',
    content: message,
    key: Date.now(),
    isFinish: true,
    reasoningData: {} as IReasoningData,
  });

  // 调用聊天API
  void sendChatMessage(message);
};

// 处理录音状态变化
const handleRecordingStatus = (recording: boolean) => {
  isRecording.value = recording;
  console.log('🎤 [index.vue] 录音状态变化:', recording);
};

// 处理关闭对话框
const handleCloseChatDialog = () => {
  showChatDialog.value = false;
  console.log('❌ [index.vue] 关闭对话框，保留会话数据以支持连续对话');

  // 不清除会话数据，保持对话连续性
  // chatMessages.value = [];
  // conversationId.value = '';

  // 停止正在进行的聊天
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  // 停止打字机
  typewriter.stop();
  isTypewriterStarted.value = false;

  // 重置聊天状态
  chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
  isStoppedByUser.value = false;
};

// 处理对话框发送消息
const handleChatDialogSend = (message: string) => {
  void handleInputSend(message);
};

// 处理停止生成 - 与relationGraph.vue完全一致
const handleStop = () => {
  console.log('� [index.vue] 停止生成');

  // 设置停止标志
  isStoppedByUser.value = true;

  // 取消流式请求
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  // 停止打字机
  typewriter.stop();
  isTypewriterStarted.value = false;

  // 标记最后一条消息为完成
  if (chatMessages.value.length > 0) {
    const lastMessage = chatMessages.value[chatMessages.value.length - 1];
    if (lastMessage && lastMessage.role === 'assistant' && !lastMessage.isFinish) {
      lastMessage.isFinish = true;
      lastMessage.content = lastMessage.content || '回答被中断';
    }
  }

  // 重置状态
  chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
};

// 清除聊天会话 - 与relationGraph.vue完全一致
const clearChatSession = async () => {
  console.log('🧹 [index.vue] 清除聊天会话');

  // 停止正在进行的聊天
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  // 停止打字机
  typewriter.stop();
  isTypewriterStarted.value = false;

  // 清除会话数据
  chatMessages.value = [];
  conversationId.value = '';

  // 重置聊天状态
  chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
  isStoppedByUser.value = false;

  // 预创建新会话（可选，也可以等到下次发送消息时再创建）
  try {
    if (currentUserId.value && currentUserId.value !== 'unknown_user') {
      console.log('🔄 [index.vue] 预创建新会话...');
      const response = await createConversation({
        user_id: currentUserId.value,
      });

      if (response.success && response.conversation_id) {
        conversationId.value = response.conversation_id;
        console.log('✅ [index.vue] 预创建新会话成功，会话ID:', conversationId.value);
      }
    }
  } catch (error) {
    console.error('❌ [index.vue] 预创建会话失败:', error);
    // 失败时不设置会话ID，等到下次发送消息时再创建
  }
};

// 处理开始体验
const handleGetStarted = () => {
  console.log('🚀 [index.vue] 开始体验');
  // 显示对话框
  showChatDialog.value = true;
};

// 处理重新生成
const handleRegenerate = (messageData: IChatStreamContent) => {
  console.log('🔄 [index.vue] 重新生成消息:', messageData);
  // TODO: 实现重新生成逻辑
};

// 处理表单提交
const handleFormSubmit = () => {
  // 表单提交时不做任何操作，因为实际的发送逻辑由inputBar组件的send事件处理
  console.log('表单提交被阻止，使用组件事件处理');
};



// 暴露方法供外部使用
defineExpose({
  goToChat,
  selectAssistant,
  selectedAssistantIndex,
  currentStep,
});
</script>

<style lang="scss" scoped>
.v-chat-container {
  // 背景现在由主题系统控制
  height: 100vh;
  overflow: hidden;
  position: relative;

  // 顶/底安全区：在根容器应用，确保顶部状态栏与底部 Home 指示条不遮挡
  // 旧版 iOS 使用 constant()，现代使用 env()，后者在下方覆盖前者
  padding-top: max(constant(safe-area-inset-top), 8px);
  padding-top: max(env(safe-area-inset-top), 8px);
  padding-bottom: max(constant(safe-area-inset-bottom), 12px);
  padding-bottom: max(env(safe-area-inset-bottom), 12px);
}

// 统一的容器样式
.ai-selection-container,
.ai-introduction-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 800px;
  height: 900px; // 固定高度
  z-index: 10;
}

.welcome-container {
  position: absolute;
  top: 70%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 800px;
  height: 85%; // 固定高度
  z-index: 10;

  // chat模式下的样式调整
  &.chat-mode {
    min-height: 64%; // 稍减高度，避免顶部被裁切
    top: 46%; // 略微下移，确保顶部可见
    margin-top: 40px;
    padding-bottom: 110px; // 为底部输入栏预留内部空间，避免被覆盖
  }
}

// 统一的内容样式
.ai-selection-content,
.ai-introduction-content,
.welcome-content {
  border-radius: 16px;
  padding: 40px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  box-shadow: var(--shadow-accent);
  transition: all 0.3s ease;
  color: var(--page-text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  text-align: center;
  height: 100%; // 填满容器
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  // chat模式下的样式调整
  &.chat-content {
    padding: 22px; // 基础内边距
    padding-top: calc(env(safe-area-inset-top) + 8px); // 顶部尽量贴近屏幕顶部并避开状态栏
    padding-bottom: calc(env(safe-area-inset-bottom) + 24px); // 底部尽量贴近对话框，预留安全区
    overflow: hidden; // 遵循圆角裁剪，防止越界
    justify-content: flex-start; // 改为顶部对齐
    gap: 15px; // 减少组件间距
    // 使用简洁的背景
    background: var(--bg-glass);
    border: 2px solid var(--border-accent);
    position: relative;

    // 环境光晕，让卡片更“游戏化”



  }
}

// welcome界面使用动态背景，不再设置固定背景色

.welcome-header {
  text-align: center;

  // chat模式下的样式调整
  &.chat-header {
    margin-bottom: 10px;
  }
}

// chat模式隐藏旧的顶部欢迎文字，避免与对白气泡冲突
.welcome-container.chat-mode .welcome-text {
  display: none !important;
}

.welcome-title-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 20px;
  margin-top: 20px;

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-left: auto; // 将右侧区域推到最右，让左侧图标与标题紧挨
  }

  .intimacy-display {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);

    .intimacy-label {
      font-size: var(--font-size-xl);
      color: var(--page-text-primary);
      font-weight: 500;
    }

    .intimacy-value {
      font-size: var(--font-size-xl);
      font-weight: 700;
      color: var(--text-primary);
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.05);
        filter: brightness(1.2);
      }
    }
  }

  // 主题切换按钮样式
  .theme-switch-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: transparent;
    border: 2px solid var(--primary-color);
    border-radius: 20px;
    color: var(--page-text-primary);
    font-size: 24px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(20px);
    box-shadow: 0 4px 12px var(--primary-color-medium);
    position: relative;
    z-index: 1000000; // 确保按钮在最上层，可以被点击

    .theme-name {
      font-size: 18px;
      white-space: nowrap;
      color: var(--primary-color);
    }
  }




}

// 统一的区域标题样式
.section-title {
  margin-bottom: 20px !important;
}

// 自定义区域标题样式 - 比cyber-title小4px
.custom-section-title {
  color: var(--text-primary);
  font-size: 30px; // 比cyber-title的42px小4px
  font-weight: 700;
  line-height: 1.4;

  margin-bottom: 20px !important;
}

// 选中的AI助手头像容器
.selected-avatar-container {
  margin-bottom: 20px; // 增加底部间距，让头像位置向下一点
  margin-top: 20px; // 增加顶部间距，让头像位置向下一点

  .selected-avatar {
    width: 180px; // 放大头像 (150px -> 170px)
    height: 180px; // 放大头像 (150px -> 170px)
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
  }
}

// 小美功能卡片容器
.xiaomei-cards-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 28px;
  margin-top: 0px;
  margin-bottom: 20px;
  width: 100%;
}

// 未来功能区域
.future-features-section {
  margin-bottom: 20px;
  width: 100%;

  // chat模式下的样式调整
  &.chat-features {
    margin-bottom: 10px; // 减少底部间距
  }
}

// AI助手排列式布局样式
.ai-assistant-grid-layout {
  position: relative;
  width: 100%;
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  perspective: 1000px; // 添加透视，营造3D层次
}

// 中心管家头像
.main-assistant-avatar {
  // 位置：在 chat 模式下更接近底部的“NPC 面前服务”的感觉
  margin-bottom: 20px;
  cursor: pointer;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  order: 4; // 放在最后


  position: relative;

  .avatar-circle.main-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  .main-avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .main-avatar-label {
    text-align: center;
    margin-top: 12px;
    font-size: 28px;
    font-weight: 500;
    color: rgb(0, 0, 0);
  }


}

// 功能区域网格布局
.features-grid {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px; // 缩小上下两排间距
  order: 1; // 在主角上方
  margin-bottom: 20px; // 与主角间距
}

// 单排容器
.features-row {
  width: 100%;
  display: flex; // 改为flex，便于做弧形/偏移
  justify-content: center;
  align-items: flex-start;
  gap: 32px; // 略收紧左右间距
  transform-style: preserve-3d;
}

// 第一行：5个头像
.features-row.row1 {
  order: 1;
  margin-bottom: 18px;
}

// 第二行：4个头像
.features-row.row2 {
  order: 2;
}

// 第三行：3个头像
.features-row.row3 {
  order: 3;
}

// 第一行 5 个功能的简化布局
.features-row.row1 .feature-item:nth-child(1) {
  transform: translateX(-30px) translateY(15px);
}
.features-row.row1 .feature-item:nth-child(2) {
  transform: translateX(-10px) translateY(5px);
}
.features-row.row1 .feature-item:nth-child(3) {
  transform: translateY(0px);
}
.features-row.row1 .feature-item:nth-child(4) {
  transform: translateX(10px) translateY(5px);
}
.features-row.row1 .feature-item:nth-child(5) {
  transform: translateX(30px) translateY(15px);
}

// 简化的行级弧度
.features-row.row2 {
  transform: rotateX(3deg);
}
.features-row.row3 {
  transform: rotateX(-3deg);
}

// 已开通功能样式（根据completed属性）
.features-row .avatar-circle:not(.disabled) {
  border-color: rgba(255, 255, 255, 0.9);
}

// 未完成功能样式
.features-row .avatar-circle.disabled {
  opacity: 0.7;
  filter: grayscale(0.35);
}

// 简化的弧形布局
// 第三行（3个头像）
.features-row.row3 .feature-item:nth-child(1) {
  transform: translateX(-15px) translateY(10px);
}
.features-row.row3 .feature-item:nth-child(2) {
  transform: translateY(-5px);
}
.features-row.row3 .feature-item:nth-child(3) {
  transform: translateX(15px) translateY(10px);
}

// 第二行（4个头像）
.features-row.row2 .feature-item:nth-child(1) {
  transform: translateX(-20px) translateY(12px);
}
.features-row.row2 .feature-item:nth-child(2) {
  transform: translateX(-8px) translateY(3px);
}
.features-row.row2 .feature-item:nth-child(3) {
  transform: translateX(8px) translateY(3px);
}
.features-row.row2 .feature-item:nth-child(4) {
  transform: translateX(20px) translateY(12px);
}

// 功能项样式
.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.25s ease;
  .avatar-circle {
    width: 86px;
    height: 86px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.75);
    background: var(--bg-glass);
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }

    // 已完成的头像样式
    &:not(.disabled) {
      border-color: rgba(255, 255, 255, 0.9);
      opacity: 1;
      filter: none;

      &:hover {
        border-color: var(--primary-color);
        box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
      }
    }

    // 未完成的头像样式
    &.disabled {
      opacity: 0.7;
      filter: grayscale(0.35);
      border-color: rgba(255, 255, 255, 0.5);

      &:hover {
        opacity: 0.85;
        filter: grayscale(0.2);
      }
    }
  }

  .feature-avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
  }

  .feature-label {
    margin-top: 10px;
    font-size: 22px;
    font-weight: 500;
    color: rgb(0, 0, 0);
    white-space: nowrap;
    text-align: center;

    // 未完成功能的标签样式
    &.incomplete-label {
      opacity: 0.7;
      color: rgb(0, 0, 0);
    }
  }

  // 轻微漂浮动画（错峰），有“生命力”的感觉

}



// 旧圆形布局类名保留为兼容，但不再使用

.future-cards-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  width: 100%;
}

// 选中头像的标题区域
.selected-avatar-header {
  text-align: center;

  .welcome-title {
    font-size: 38px;
    font-weight: 600;
    color: var(--page-text-secondary);

    margin: 0;
    margin-bottom: 50px;
    margin-top: 50px;
  }
}

// 介绍页面底部文字
.introduction-bottom-text {
  text-align: center;
  margin-top: 5px;

  p {
    font-size: 24px;
    font-weight: 500;
    color: var(--page-text-primary);

    margin: 0;
    opacity: 0.9;
  }
}

// 欢迎文字
.welcome-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .welcome-line {
    font-size: 36px; // 增加4px (24px -> 28px)
    font-weight: 600;
    margin-bottom: 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);

    &:last-child {
      margin-bottom: 0;
      font-size: 26px; // 增加4px (20px -> 24px)
      opacity: 0.9;
      margin-top: 20px;
    }
  }
}

// Swiper容器
.avatar-swiper-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  .avatar-swiper {
    width: 100%;
    max-width: 600px; // 增加宽度以容纳6个头像 (500px -> 600px)
    height: 120px;
    padding: 20px 0;
  }

  .avatar-slide {
    width: 90px !important; // 放大slide容器 (80px -> 90px)
    height: 90px; // 放大slide容器 (80px -> 90px)
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;

    &.active {
      transform: scale(1.2);

      .swiper-avatar {
        border-color: var(--accent-color-strong);
      }
    }

    .swiper-avatar {
      width: 70px;
      height: 70px;
      border-radius: 50%;
      object-fit: cover;
      border: 3px solid rgba(255, 255, 255, 0.5);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: rgba(255, 255, 255, 0.8);
        transform: scale(1.05);
      }
    }
  }
}

// 选择按钮
.selection-button-container {
  margin-top: auto;

  .selection-btn {
    background: var(--primary-color-medium);
    border: 2px solid var(--primary-color-strong);
    border-radius: 32px;
    margin-bottom: 30px;
    padding: 20px 40px;
    color: var(--page-text-primary);
    font-size: 26px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    min-height: 60px;

    &:hover {
      background: var(--primary-color-strong);
      transform: translateY(-1px);
    }
  }
}

.welcome-icon {
  flex-shrink: 0; // 防止头像被压缩
  display: flex;
  align-items: center;
  justify-content: center;

  .assistant-avatar {
    width: 80px; // 调整头像大小以适应横向布局
    height: 80px; // 调整头像大小以适应横向布局
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid var(--accent-color-medium);
    box-shadow: var(--shadow-accent);
  }
}

// 介绍页面Tab切换样式
.intro-tab-switch {
  position: relative;
  display: flex;
  background: var(--primary-color-light);
  border: 2px solid var(--primary-color);
  border-radius: 36px; // 再增大圆角 (32px -> 36px)
  padding: 10px; // 再增大内边距 (8px -> 10px)
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 100px;
  overflow: hidden;
  width: 500px; // 适度增大宽度以适应更大字体
  min-width: 420px;
  height: 72px; // 再增大高度 (64px -> 72px)
  margin: 0 auto 30px auto;

  &:hover {
    background: var(--primary-color-light);
  }

  .tab-slider {
    position: absolute;
    top: 10px;
    left: 10px;
    width: calc(50% - 10px);
    height: calc(100% - 20px);
    background: var(--primary-color);
    border-radius: 30px;
    transition: transform 0.3s ease;

    &.slide-right {
      transform: translateX(100%);
    }
  }

  .tab-option {
    flex: 1;
    padding: 16px 30px; // 再增大内边距 (14px 26px -> 16px 30px)
    text-align: center;
    font-size: 30px; // 再增加2px (28px -> 30px)
    font-weight: 500;
    color: var(--page-text-secondary);
    transition: color 0.3s ease;
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    &.active {
      color: var(--page-text-primary);
    }
  }
}

// 介绍内容样式
.intro-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 380px; // 设置最小高度，防止挤占tab-switch空间
  max-height: 380px; // 设置最大高度，保持一致性

  .video-intro {
    width: 100%;
    height: 100%; // 填满容器高度
    display: flex;
    align-items: center;
    justify-content: center;

    .video-container {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 20px;
      overflow: hidden;
      background: rgba(0, 0, 0, 0.3);
      border: 2px solid var(--accent-color-medium);
      box-shadow:
        0 0 15px var(--accent-color-light),
        inset 0 0 20px var(--accent-color-light);

      .intro-video {
        width: 100%;
        height: 100%;
        object-fit: contain;
        border-radius: 18px; // 稍小于容器圆角
        background: rgba(0, 0, 0, 0.8);
      }
    }
  }

  .text-intro {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px; // 增大圆角 (16px -> 20px)
    padding: 35px; // 增大内边距 (30px -> 35px)
    backdrop-filter: blur(10px);
    width: 100%;
    height: 100%; // 设置固定高度，与video-placeholder保持一致
    overflow-y: auto;
    // 添加有阴影的边框
    border: 2px solid var(--accent-color-medium);
    box-shadow:
      0 0 15px var(--accent-color-light),
      inset 0 0 20px var(--accent-color-light);
    box-sizing: border-box; // 确保padding不会影响总高度

    p {
      font-size: 20px; // 再增加2px (18px -> 20px)
      line-height: 1.8; // 增加行高便于阅读
      color: var(--page-text-secondary);
      margin: 0;
      text-align: left; // 左对齐便于阅读长文本
      white-space: pre-line; // 保持换行格式
    }
  }
}

// 介绍按钮
.introduction-button-container {
  margin-top: auto;

  .introduction-btn {
    background: var(--primary-color-medium); // 改为低透明度，与选择按钮保持一致
    border: 2px solid var(--primary-color-strong); // 边框也调整透明度
    border-radius: 32px; // 增大圆角 (28px -> 32px)
    margin-bottom: 5px; // 添加底部间距，与选择按钮保持一致
    padding: 20px 40px; // 放大按钮 (16px 32px -> 20px 40px)
    color: var(--page-text-primary);
    font-size: 26px; // 调整为与选择按钮一致的字体大小
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    min-height: 60px; // 增加最小高度

    &:hover {
      background: var(--primary-color-strong); // hover状态也调整为低透明度
      transform: translateY(-2px);
      box-shadow: 0 4px 12px var(--primary-color-strong);
    }
  }
}

// 亲密度模块样式
.intimacy-container {
  margin-top: 80px;
  padding: 0 10px;
}

.intimacy-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  padding: 20px 24px;
  background: var(--bg-glass);
  border: 2px solid var(--border-accent);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-accent);
}

.intimacy-curve {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  .polyline-icon {
    width: 50px;
    height: 40px;
    object-fit: contain;
    margin-right: 10px;
  }
}

.intimacy-text-container {
  flex: 1;
  text-align: left;
}

.intimacy-title {
  color: var(--text-primary);
  font-size: 24px;
  font-weight: 400;
  margin-bottom: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.intimacy-description {
  color: var(--page-text-primary);
  font-size: 20px;
  font-weight: 300;
  line-height: 1.4;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

// 亲密度弹窗样式 - 与index页面统一
.intimacy-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.intimacy-popup-content {
  border-radius: 16px;
  padding: 40px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  box-shadow:
    0 0 12px var(--accent-color-strong),
    0 8px 32px rgba(0, 0, 0, 0.7),
    0 4px 16px rgba(0, 0, 0, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  max-width: 90vw;
  max-height: 80vh;
  width: 500px;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
}

// AI助手头像容器 - 与index页面统一
.intimacy-popup-avatar-container {
  margin-bottom: 20px;
  margin-top: 20px;

  .intimacy-popup-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
  }
}

.intimacy-popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.intimacy-popup-title {
  color: var(--primary-color);
  font-size: 28px; // 增加4px (24px -> 28px)
  font-weight: 600;
  margin: 0;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.intimacy-close-btn {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 10;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.1);
  }

  .close-icon {
    color: white;
    font-size: 24px; // 增加4px (20px -> 24px)
    font-weight: bold;
    line-height: 1;
  }
}

.intimacy-popup-body {
  .intimacy-description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 22px; // 增加4px (18px -> 22px)
    line-height: 1.6;
    margin: 0;
    text-align: center;
  }
}

// 功能锁定弹窗样式 - 与relationGraph页面的deleteEventDialog保持一致
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
  animation: fadeIn 0.3s ease-out;
}

.dialog-container {
  background: rgba(1, 28, 32, 0.6);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  width: 90%;
  max-width: 500px;
  min-height: 300px;
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-accent);
  box-shadow: 0 0 20px var(--accent-color-medium);
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.dialog-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  height: 80px;

  .dialog-title {
    color: var(--primary-color);
    font-size: 32px;
    font-weight: 600;
  }
}

.dialog-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 32px;

  .lock-message {
    color: rgba(255, 255, 255, 0.9);
    font-size: 28px;
    font-weight: 500;
    line-height: 1.6;
    letter-spacing: 0.5px;
  }
}

.dialog-footer {
  display: flex;
  gap: 20px;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);

  .confirm-btn {
    flex: 1;
    padding: 16px 16px;
    border-radius: 20px;
    font-size: 28px;
    font-weight: 400;
    border: 2px solid;
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    letter-spacing: 1px;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    color: var(--primary-color);
    border-color: var(--primary-color);

    &:hover:not(:disabled) {
      background: var(--primary-color-light);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px var(--primary-color-medium);
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// chat模式下的亲密度容器样式调整
.intimacy-container.chat-intimacy {
  margin-bottom: 0; // 移除底部间距
}

// 底部输入框样式 - 固定在v-chat-container内部底部
.footer {
  position: absolute;
  // 使用安全区避让底部系统 UI
  bottom: constant(safe-area-inset-bottom);
  bottom: env(safe-area-inset-bottom);
  left: 0;
  right: 0;
  z-index: 1100; // 设置比弹窗更高的z-index，确保inputBar始终在最上层

  // 同时给容器增加内边距，防止内容紧贴安全区
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  .input-wrapper {
    width: 100%;
    padding: 0px;
    background: transparent;
  }
}

/* 主题选择器包装器样式 */
.theme-selector-wrapper {
  position: relative;
  z-index: 9999999 !important; /* 确保主题选择器在最上层，高于所有feature-item */
  isolation: isolate; /* 创建独立的层叠上下文 */
}

.tone-section {
  margin-bottom: 10px;
}

.tone-title,
.palette-title {
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 8px;
}

.tone-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
}

.tone-item {
  padding: 8px 6px;
  font-size: 12px;
  line-height: 1;
  border-radius: 10px;
  border: 1px solid #2a3a6b;
  background: #18213d; /* 纯色，不透明 */
  color: rgba(255, 255, 255, 0.92);
  cursor: pointer;
}
.tone-item.active {
  background: #1d3b73; /* 纯色，不透明 */
  border-color: #3b82f6;
}

.palette-section {
  margin-top: 6px;
}

.palette-grid {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 10px;
}

.theme-item {
  display: flex;
  flex-direction: column; /* 名称放到色块下方 */
  align-items: center;
  gap: 8px;
  padding: 10px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.12);
  background: #0c162d !important; /* 不透明卡片背景 */
  cursor: pointer;
}
.theme-item:hover {
  background: #0b1427;
}
.theme-item.active {
  border-color: rgba(59, 130, 246, 0.55);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25) inset;
}

.swatches {
  display: flex;
  gap: 6px;
  margin-bottom: 2px;
}
.swatch {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}
.theme-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.92);
  text-align: center;
}

// 老董假装说话样式 - 作为输入框的下半部分
.laodong-fake-speaking {
  padding: 12px 20px; // 与输入框内部padding一致
  display: flex;
  justify-content: flex-start;
  // 使用与输入框相同的背景
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.1) 60%,
    rgba(0, 0, 0, 0.3) 100%
  );
  border: 2px solid var(--border-accent); // 与输入框相同的边框
  border-top: none; // 去掉上边框，与输入框连接
  border-radius: 0 0 20px 20px; // 只有下方圆角，与输入框形成整体
  margin-top: -2px; // 与输入框无缝连接
  backdrop-filter: blur(20px); // 与输入框相同的毛玻璃效果
  box-shadow: var(--shadow-strong); // 与输入框相同的阴影

  .fake-speaking-container {
    display: flex;
    align-items: center; // 改为居中对齐，一行显示
    gap: 12px;
    width: 100%; // 占满宽度

    .laodong-avatar {
      width: 48px; // 适中的头像大小
      height: 48px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;
      border: 2px solid var(--border-accent);
      box-shadow: var(--shadow-medium);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .fake-speaking-content {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .fake-speaking-text {
        color: var(--text-primary);
        font-size: 20px; // 增大字体
        font-weight: 500;
        line-height: 1.2;
        white-space: nowrap; // 不换行
      }

      .fake-speaking-dots {
        display: flex;
        gap: 4px;
        align-items: center;

        .dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: var(--accent-color);
          animation: fakeSpeakingPulse 1.5s ease-in-out infinite;

          &:nth-child(1) {
            animation-delay: 0s;
          }

          &:nth-child(2) {
            animation-delay: 0.3s;
          }

          &:nth-child(3) {
            animation-delay: 0.6s;
          }
        }
      }
    }
  }
}

// 假装说话动画
@keyframes fakeSpeakingPulse {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

// 移动端适配
@media (max-width: 768px) {
  .laodong-fake-speaking {
    padding: 10px 16px; // 移动端保持背景padding

    .fake-speaking-container {
      gap: 10px;

      .laodong-avatar {
        width: 40px; // 移动端适中尺寸
        height: 40px;
      }

      .fake-speaking-content {
        gap: 6px;

        .fake-speaking-text {
          font-size: 18px; // 移动端保持较大字体
        }

        .fake-speaking-dots {
          gap: 3px;

          .dot {
            width: 5px;
            height: 5px;
          }
        }
      }
    }
  }
}
</style>
